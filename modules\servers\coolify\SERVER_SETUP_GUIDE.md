# 🖥️ WHMCS Server Setup Guide for Coolify

## Problem Solved

WHMCS was rejecting the hostname field with the error:
```
❌ The Hostname format is invalid.
```

This happened because WHMCS validates hostname fields and doesn't accept JSON format.

## ✅ New WHMCS-Friendly Solution

Instead of using J<PERSON><PERSON> in the hostname field, we now use **standard WHMCS server fields** that don't trigger validation errors.

## 📋 Server Configuration Fields

### Primary Fields
- **Server Name**: Descriptive name (e.g., "Coolify US East")
- **Hostname**: Coolify domain (e.g., "coolify.yourdomain.com")
- **IP Address**: Full Coolify URL (e.g., "https://coolify.yourdomain.com")
- **Access Hash**: Coolify API Bearer Token

### Configuration Fields (Using Nameserver Fields)
- **Nameserver 1**: Project UUID
- **Nameserver 2**: Server UUID
- **Nameserver 3**: Destination UUID
- **Nameserver 4**: Environment Name (e.g., "production")
- **Nameserver 5**: Base Domain (e.g., "customers.yourdomain.com")

## 🛠️ Step-by-Step Setup

### Step 1: Access WHMCS Admin
1. Go to **WHMCS Admin Area**
2. Navigate to **Setup > Products/Services > Servers**
3. Click **"Add New Server"**

### Step 2: Basic Server Information
```
Server Name: Coolify Production
Hostname: coolify.yourdomain.com
IP Address: https://coolify.yourdomain.com
Type: Other
Active: Yes
```

### Step 3: API Authentication
```
Access Hash: your-coolify-api-bearer-token
```

### Step 4: Coolify Configuration (Nameserver Fields)
```
Nameserver 1: 01234567-89ab-cdef-0123-456789abcdef  (Project UUID)
Nameserver 2: 01234567-89ab-cdef-0123-456789abcdef  (Server UUID)
Nameserver 3: 01234567-89ab-cdef-0123-456789abcdef  (Destination UUID)
Nameserver 4: production                             (Environment Name)
Nameserver 5: customers.yourdomain.com               (Base Domain)
```

### Step 5: Save and Test
1. Click **"Save Changes"**
2. Click **"Test Connection"** (if available)
3. Verify no validation errors

## 🌍 Multi-Region Example

### US East Server
```
Server Name: Coolify US East
Hostname: coolify-us.yourdomain.com
IP Address: https://coolify-us.yourdomain.com
Access Hash: us-api-token-here

Nameserver 1: us-project-uuid-here
Nameserver 2: us-server-uuid-here
Nameserver 3: us-destination-uuid-here
Nameserver 4: production
Nameserver 5: us.customers.yourdomain.com
```

### EU West Server
```
Server Name: Coolify EU West
Hostname: coolify-eu.yourdomain.com
IP Address: https://coolify-eu.yourdomain.com
Access Hash: eu-api-token-here

Nameserver 1: eu-project-uuid-here
Nameserver 2: eu-server-uuid-here
Nameserver 3: eu-destination-uuid-here
Nameserver 4: production
Nameserver 5: eu.customers.yourdomain.com
```

## 🔍 How the Module Reads Configuration

The module now reads configuration in this order:

### 1. Coolify URL
- **Primary**: IP Address field
- **Fallback**: Hostname field

### 2. UUIDs and Settings
- **Project UUID**: Nameserver 1
- **Server UUID**: Nameserver 2
- **Destination UUID**: Nameserver 3
- **Environment**: Nameserver 4
- **Base Domain**: Nameserver 5

### 3. Advanced Configuration (Optional)
- **JSON in Server Name**: For complex configurations
- **Automatic Defaults**: For missing values

## 📝 Configuration Examples

### Minimal Configuration
```
IP Address: https://coolify.yourdomain.com
Access Hash: your-api-token
Nameserver 1: project-uuid
Nameserver 2: server-uuid
Nameserver 3: destination-uuid
```

### Complete Configuration
```
Server Name: Coolify Production
Hostname: coolify.yourdomain.com
IP Address: https://coolify.yourdomain.com
Access Hash: your-api-token

Nameserver 1: 01234567-89ab-cdef-0123-456789abcdef
Nameserver 2: 01234567-89ab-cdef-0123-456789abcdef
Nameserver 3: 01234567-89ab-cdef-0123-456789abcdef
Nameserver 4: production
Nameserver 5: customers.yourdomain.com
```

### Advanced JSON Configuration (Optional)
```
Server Name: {"project_uuid":"uuid","custom_setting":"value"}
IP Address: https://coolify.yourdomain.com
Access Hash: your-api-token
```

## 🔧 Getting Your UUIDs

### From Coolify Dashboard
1. **Project UUID**: Go to Projects → Select Project → Copy UUID from URL
2. **Server UUID**: Go to Servers → Select Server → Copy UUID from URL
3. **Destination UUID**: Go to Destinations → Select Destination → Copy UUID

### From Coolify API
```bash
# Get Projects
curl -H "Authorization: Bearer YOUR_TOKEN" https://coolify.yourdomain.com/api/v1/projects

# Get Servers
curl -H "Authorization: Bearer YOUR_TOKEN" https://coolify.yourdomain.com/api/v1/servers

# Get Destinations
curl -H "Authorization: Bearer YOUR_TOKEN" https://coolify.yourdomain.com/api/v1/destinations
```

## ✅ Validation Checklist

Before saving your server configuration:

- ✅ **Hostname**: Valid domain format (no JSON)
- ✅ **IP Address**: Full URL with https://
- ✅ **Access Hash**: Valid Coolify API token
- ✅ **Nameserver 1**: Valid Project UUID (36 characters)
- ✅ **Nameserver 2**: Valid Server UUID (36 characters)
- ✅ **Nameserver 3**: Valid Destination UUID (36 characters)
- ✅ **Nameserver 4**: Environment name (e.g., "production")
- ✅ **Nameserver 5**: Valid domain for customers

## 🚨 Common Mistakes to Avoid

### ❌ Don't Do This
```
Hostname: {"project_uuid":"..."}  ← JSON not allowed
IP Address: coolify.com           ← Missing https://
Access Hash: (empty)              ← Missing API token
Nameserver 1: project-name        ← Use UUID, not name
```

### ✅ Do This Instead
```
Hostname: coolify.yourdomain.com
IP Address: https://coolify.yourdomain.com
Access Hash: your-api-token-here
Nameserver 1: 01234567-89ab-cdef-0123-456789abcdef
```

## 🔍 Troubleshooting

### "Hostname format is invalid"
- **Cause**: JSON or invalid characters in hostname
- **Solution**: Use plain domain name only

### "Connection failed"
- **Cause**: Wrong URL or API token
- **Solution**: Verify IP Address and Access Hash

### "Service creation failed"
- **Cause**: Invalid UUIDs
- **Solution**: Check nameserver fields have correct UUIDs

### "Missing configuration"
- **Cause**: Empty nameserver fields
- **Solution**: Fill in all required nameserver fields

## 🎯 Benefits of New Approach

### For WHMCS Compatibility
- ✅ **No validation errors** - Uses standard WHMCS fields
- ✅ **Clean interface** - Familiar WHMCS server setup
- ✅ **Easy management** - Standard server management tools

### For Multiple Coolify Instances
- ✅ **Easy scaling** - Add servers without product changes
- ✅ **Geographic distribution** - Multiple regions supported
- ✅ **Load balancing** - Distribute services across instances

### For Maintenance
- ✅ **Clear configuration** - Easy to understand and modify
- ✅ **No JSON parsing** - Simpler and more reliable
- ✅ **Standard fields** - Uses WHMCS built-in functionality

## 📚 Next Steps

1. **Set up your first server** using this guide
2. **Test the configuration** by creating a test service
3. **Add additional servers** for scaling
4. **Configure products** to use the new servers

The new server configuration approach is **WHMCS-friendly** and **validation-error-free**! 🎉
