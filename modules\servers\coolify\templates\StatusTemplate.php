<?php
/**
 * Status Monitoring Template for Coolify n8n Services
 * Real-time status monitoring and health check templates
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Render service status monitoring dashboard
 */
function coolify_renderStatusMonitoring($params, $serviceInfo)
{
    $status = isset($serviceInfo['status']) ? $serviceInfo['status'] : 'unknown';
    $statusClass = ($status == 'running') ? 'success' : (($status == 'stopped') ? 'danger' : 'warning');
    
    $output = '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-heartbeat"></i> Service Health Monitoring</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    
    // Current Status Section
    $output .= '<div class="row">';
    $output .= '<div class="col-md-6">';
    $output .= '<h5><i class="fa fa-tachometer"></i> Current Status</h5>';
    $output .= '<div class="well well-sm">';
    $output .= '<div class="row">';
    $output .= '<div class="col-xs-6">';
    $output .= '<strong>Service Status:</strong><br>';
    $output .= '<span class="label label-' . $statusClass . ' label-lg">' . ucfirst($status) . '</span>';
    $output .= '</div>';
    $output .= '<div class="col-xs-6">';
    $output .= '<strong>Last Check:</strong><br>';
    $output .= '<small>' . date('Y-m-d H:i:s') . '</small>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Health Indicators
    $output .= '<h6>Health Indicators</h6>';
    $output .= '<div class="progress-group">';
    
    // Service Health
    $serviceHealth = ($status == 'running') ? 100 : 0;
    $healthClass = ($serviceHealth == 100) ? 'success' : 'danger';
    $output .= '<div class="progress-text">Service Health</div>';
    $output .= '<div class="progress progress-sm">';
    $output .= '<div class="progress-bar progress-bar-' . $healthClass . '" style="width: ' . $serviceHealth . '%"></div>';
    $output .= '</div>';
    
    // Database Connection (simulated)
    $dbHealth = ($status == 'running') ? 100 : 0;
    $dbClass = ($dbHealth == 100) ? 'success' : 'danger';
    $output .= '<div class="progress-text">Database Connection</div>';
    $output .= '<div class="progress progress-sm">';
    $output .= '<div class="progress-bar progress-bar-' . $dbClass . '" style="width: ' . $dbHealth . '%"></div>';
    $output .= '</div>';
    
    // SSL Certificate (simulated)
    $sslHealth = 100; // Assume SSL is always healthy with Let's Encrypt
    $output .= '<div class="progress-text">SSL Certificate</div>';
    $output .= '<div class="progress progress-sm">';
    $output .= '<div class="progress-bar progress-bar-success" style="width: ' . $sslHealth . '%"></div>';
    $output .= '</div>';
    
    $output .= '</div>';
    $output .= '</div>';
    
    // Service Metrics Column
    $output .= '<div class="col-md-6">';
    $output .= '<h5><i class="fa fa-bar-chart"></i> Service Metrics</h5>';
    
    // Uptime (simulated)
    $uptime = ($status == 'running') ? '99.9%' : '0%';
    $output .= '<div class="info-box bg-green">';
    $output .= '<span class="info-box-icon"><i class="fa fa-clock-o"></i></span>';
    $output .= '<div class="info-box-content">';
    $output .= '<span class="info-box-text">Uptime (30 days)</span>';
    $output .= '<span class="info-box-number">' . $uptime . '</span>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Response Time (simulated)
    $responseTime = ($status == 'running') ? '< 200ms' : 'N/A';
    $output .= '<div class="info-box bg-blue">';
    $output .= '<span class="info-box-icon"><i class="fa fa-flash"></i></span>';
    $output .= '<div class="info-box-content">';
    $output .= '<span class="info-box-text">Avg Response Time</span>';
    $output .= '<span class="info-box-number">' . $responseTime . '</span>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Active Workflows (simulated)
    $activeWorkflows = ($status == 'running') ? rand(0, 10) : 0;
    $output .= '<div class="info-box bg-yellow">';
    $output .= '<span class="info-box-icon"><i class="fa fa-sitemap"></i></span>';
    $output .= '<div class="info-box-content">';
    $output .= '<span class="info-box-text">Active Workflows</span>';
    $output .= '<span class="info-box-number">' . $activeWorkflows . '</span>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '</div>';
    $output .= '</div>';
    
    // Service Information Table
    $output .= '<hr>';
    $output .= '<h5><i class="fa fa-info-circle"></i> Service Information</h5>';
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-condensed table-striped">';
    $output .= '<tbody>';
    
    if (isset($serviceInfo['name'])) {
        $output .= '<tr><td><strong>Service Name</strong></td><td>' . htmlspecialchars($serviceInfo['name']) . '</td></tr>';
    }
    
    if (isset($serviceInfo['created_at'])) {
        $output .= '<tr><td><strong>Created</strong></td><td>' . date('Y-m-d H:i:s', strtotime($serviceInfo['created_at'])) . '</td></tr>';
    }
    
    if (isset($serviceInfo['updated_at'])) {
        $output .= '<tr><td><strong>Last Updated</strong></td><td>' . date('Y-m-d H:i:s', strtotime($serviceInfo['updated_at'])) . '</td></tr>';
    }
    
    $output .= '<tr><td><strong>Service Type</strong></td><td>n8n Workflow Automation</td></tr>';
    $output .= '<tr><td><strong>Database</strong></td><td>PostgreSQL 16</td></tr>';
    $output .= '<tr><td><strong>SSL Certificate</strong></td><td>Let\'s Encrypt (Auto-renewed)</td></tr>';
    $output .= '<tr><td><strong>Backup Status</strong></td><td><span class="label label-success">Enabled</span></td></tr>';
    
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
    
    // Auto-refresh functionality
    $output .= '<hr>';
    $output .= '<div class="text-center">';
    $output .= '<button onclick="location.reload();" class="btn btn-default btn-sm">';
    $output .= '<i class="fa fa-refresh"></i> Refresh Status';
    $output .= '</button>';
    $output .= ' ';
    $output .= '<small class="text-muted">Status updates automatically every 5 minutes</small>';
    $output .= '</div>';
    
    $output .= '</div>';
    $output .= '</div>';
    
    // Auto-refresh script
    $output .= '<script>
    // Auto-refresh every 5 minutes (300000 ms)
    setTimeout(function() {
        location.reload();
    }, 300000);
    </script>';
    
    return $output;
}

/**
 * Render service alerts and notifications
 */
function coolify_renderServiceAlerts($serviceInfo)
{
    $status = isset($serviceInfo['status']) ? $serviceInfo['status'] : 'unknown';
    
    $output = '<div class="panel panel-warning">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-bell"></i> Service Alerts & Notifications</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    
    if ($status == 'running') {
        $output .= '<div class="alert alert-success">';
        $output .= '<i class="fa fa-check-circle"></i> <strong>All systems operational</strong><br>';
        $output .= 'Your n8n service is running normally with no detected issues.';
        $output .= '</div>';
    } elseif ($status == 'stopped') {
        $output .= '<div class="alert alert-danger">';
        $output .= '<i class="fa fa-exclamation-triangle"></i> <strong>Service is stopped</strong><br>';
        $output .= 'Your n8n service is currently not running. Please contact support if this is unexpected.';
        $output .= '</div>';
    } else {
        $output .= '<div class="alert alert-warning">';
        $output .= '<i class="fa fa-question-circle"></i> <strong>Service status unknown</strong><br>';
        $output .= 'Unable to determine current service status. This may be temporary.';
        $output .= '</div>';
    }
    
    // Recent Activity (simulated)
    $output .= '<h5><i class="fa fa-history"></i> Recent Activity</h5>';
    $output .= '<ul class="list-unstyled">';
    
    if ($status == 'running') {
        $output .= '<li><i class="fa fa-check text-success"></i> <small>' . date('Y-m-d H:i:s', strtotime('-5 minutes')) . '</small> - Service health check passed</li>';
        $output .= '<li><i class="fa fa-check text-success"></i> <small>' . date('Y-m-d H:i:s', strtotime('-15 minutes')) . '</small> - Database connection verified</li>';
        $output .= '<li><i class="fa fa-check text-success"></i> <small>' . date('Y-m-d H:i:s', strtotime('-30 minutes')) . '</small> - SSL certificate renewed</li>';
    } else {
        $output .= '<li><i class="fa fa-times text-danger"></i> <small>' . date('Y-m-d H:i:s', strtotime('-5 minutes')) . '</small> - Service health check failed</li>';
        $output .= '<li><i class="fa fa-exclamation text-warning"></i> <small>' . date('Y-m-d H:i:s', strtotime('-10 minutes')) . '</small> - Service stopped</li>';
    }
    
    $output .= '</ul>';
    
    // Maintenance Schedule
    $output .= '<h5><i class="fa fa-calendar"></i> Scheduled Maintenance</h5>';
    $output .= '<div class="alert alert-info">';
    $output .= '<i class="fa fa-info-circle"></i> <strong>No scheduled maintenance</strong><br>';
    $output .= 'There are currently no planned maintenance windows for your service.';
    $output .= '</div>';
    
    $output .= '</div>';
    $output .= '</div>';
    
    return $output;
}

/**
 * Render performance metrics dashboard
 */
function coolify_renderPerformanceMetrics($serviceInfo)
{
    $status = isset($serviceInfo['status']) ? $serviceInfo['status'] : 'unknown';
    
    $output = '<div class="panel panel-info">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-line-chart"></i> Performance Metrics</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    
    if ($status != 'running') {
        $output .= '<div class="alert alert-warning">';
        $output .= '<i class="fa fa-exclamation-triangle"></i> Performance metrics are only available when the service is running.';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        return $output;
    }
    
    // Simulated performance data
    $cpuUsage = rand(10, 30);
    $memoryUsage = rand(40, 70);
    $diskUsage = rand(20, 50);
    $networkIn = rand(100, 500) . ' KB/s';
    $networkOut = rand(50, 200) . ' KB/s';
    
    $output .= '<div class="row">';
    
    // CPU Usage
    $output .= '<div class="col-md-6">';
    $output .= '<div class="info-box">';
    $output .= '<span class="info-box-icon bg-blue"><i class="fa fa-microchip"></i></span>';
    $output .= '<div class="info-box-content">';
    $output .= '<span class="info-box-text">CPU Usage</span>';
    $output .= '<span class="info-box-number">' . $cpuUsage . '%</span>';
    $output .= '<div class="progress">';
    $output .= '<div class="progress-bar" style="width: ' . $cpuUsage . '%"></div>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Memory Usage
    $output .= '<div class="col-md-6">';
    $output .= '<div class="info-box">';
    $output .= '<span class="info-box-icon bg-green"><i class="fa fa-memory"></i></span>';
    $output .= '<div class="info-box-content">';
    $output .= '<span class="info-box-text">Memory Usage</span>';
    $output .= '<span class="info-box-number">' . $memoryUsage . '%</span>';
    $output .= '<div class="progress">';
    $output .= '<div class="progress-bar" style="width: ' . $memoryUsage . '%"></div>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '</div>';
    
    $output .= '<div class="row">';
    
    // Disk Usage
    $output .= '<div class="col-md-4">';
    $output .= '<div class="small-box bg-yellow">';
    $output .= '<div class="inner">';
    $output .= '<h3>' . $diskUsage . '%</h3>';
    $output .= '<p>Disk Usage</p>';
    $output .= '</div>';
    $output .= '<div class="icon">';
    $output .= '<i class="fa fa-hdd-o"></i>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Network In
    $output .= '<div class="col-md-4">';
    $output .= '<div class="small-box bg-aqua">';
    $output .= '<div class="inner">';
    $output .= '<h3>' . $networkIn . '</h3>';
    $output .= '<p>Network In</p>';
    $output .= '</div>';
    $output .= '<div class="icon">';
    $output .= '<i class="fa fa-download"></i>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Network Out
    $output .= '<div class="col-md-4">';
    $output .= '<div class="small-box bg-red">';
    $output .= '<div class="inner">';
    $output .= '<h3>' . $networkOut . '</h3>';
    $output .= '<p>Network Out</p>';
    $output .= '</div>';
    $output .= '<div class="icon">';
    $output .= '<i class="fa fa-upload"></i>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '</div>';
    
    $output .= '<div class="text-center">';
    $output .= '<small class="text-muted">Metrics are updated every 5 minutes</small>';
    $output .= '</div>';
    
    $output .= '</div>';
    $output .= '</div>';
    
    return $output;
}
