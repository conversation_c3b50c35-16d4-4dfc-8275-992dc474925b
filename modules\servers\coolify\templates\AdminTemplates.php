<?php
/**
 * Admin Templates for Coolify Module
 * Enhanced admin interface templates for better service management
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Render enhanced admin service overview
 */
function coolify_renderAdminServiceOverview($params, $serviceInfo, $serverResources = null)
{
    $output = '<div class="row">';
    
    // Main Service Information Panel
    $output .= '<div class="col-md-8">';
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4><i class="fa fa-server"></i> n8n Service Management</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    
    if ($serviceInfo) {
        $status = isset($serviceInfo['status']) ? $serviceInfo['status'] : 'unknown';
        $statusClass = ($status == 'running') ? 'success' : (($status == 'stopped') ? 'danger' : 'warning');
        
        // Service Status Section
        $output .= '<div class="row">';
        $output .= '<div class="col-md-6">';
        $output .= '<h5><i class="fa fa-info-circle"></i> Service Details</h5>';
        $output .= '<table class="table table-condensed">';
        $output .= '<tr><td><strong>Service Name:</strong></td><td>' . htmlspecialchars($serviceInfo['name']) . '</td></tr>';
        $output .= '<tr><td><strong>Status:</strong></td><td><span class="label label-' . $statusClass . '">' . ucfirst($status) . '</span></td></tr>';
        $output .= '<tr><td><strong>Created:</strong></td><td>' . (isset($serviceInfo['created_at']) ? date('Y-m-d H:i:s', strtotime($serviceInfo['created_at'])) : 'N/A') . '</td></tr>';
        $output .= '<tr><td><strong>Last Updated:</strong></td><td>' . (isset($serviceInfo['updated_at']) ? date('Y-m-d H:i:s', strtotime($serviceInfo['updated_at'])) : 'N/A') . '</td></tr>';
        $output .= '</table>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<h5><i class="fa fa-globe"></i> Access Information</h5>';
        $output .= '<table class="table table-condensed">';
        $output .= '<tr><td><strong>Customer Domain:</strong></td><td>' . htmlspecialchars($params['domain']) . '</td></tr>';
        
        if (isset($serviceInfo['fqdn']) && !empty($serviceInfo['fqdn'])) {
            $output .= '<tr><td><strong>Actual FQDN:</strong></td><td>' . htmlspecialchars($serviceInfo['fqdn']) . '</td></tr>';
        }
        
        $output .= '<tr><td><strong>Service UUID:</strong></td><td><code style="font-size: 11px;">' . htmlspecialchars(getServiceUuid($params['serviceid'])) . '</code></td></tr>';
        $output .= '<tr><td><strong>Customer ID:</strong></td><td>' . htmlspecialchars($params['userid']) . '</td></tr>';
        $output .= '</table>';
        
        $output .= '<div class="text-center" style="margin-top: 15px;">';
        $output .= '<a href="https://' . htmlspecialchars($params['domain']) . '" target="_blank" class="btn btn-primary btn-sm">';
        $output .= '<i class="fa fa-external-link"></i> Open n8n Dashboard';
        $output .= '</a>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Environment Variables Section
        if (isset($serviceInfo['environment_variables']) && !empty($serviceInfo['environment_variables'])) {
            $output .= '<hr>';
            $output .= '<h5><i class="fa fa-cogs"></i> Environment Variables</h5>';
            $output .= '<div class="table-responsive">';
            $output .= '<table class="table table-condensed table-striped">';
            $output .= '<thead><tr><th>Variable</th><th>Value</th></tr></thead>';
            $output .= '<tbody>';
            foreach ($serviceInfo['environment_variables'] as $key => $value) {
                $displayValue = (strpos($key, 'PASSWORD') !== false || strpos($key, 'SECRET') !== false || strpos($key, 'TOKEN') !== false) ? '••••••••' : $value;
                $output .= '<tr>';
                $output .= '<td><code>' . htmlspecialchars($key) . '</code></td>';
                $output .= '<td>' . htmlspecialchars($displayValue) . '</td>';
                $output .= '</tr>';
            }
            $output .= '</tbody>';
            $output .= '</table>';
            $output .= '</div>';
        }
        
    } else {
        $output .= '<div class="alert alert-warning">';
        $output .= '<i class="fa fa-exclamation-triangle"></i> Unable to retrieve service information from Coolify API.';
        $output .= '</div>';
    }
    
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Sidebar with Actions and Server Info
    $output .= '<div class="col-md-4">';
    $output .= coolify_renderAdminSidebar($params, $serviceInfo, $serverResources);
    $output .= '</div>';
    
    $output .= '</div>';
    
    return $output;
}

/**
 * Render admin sidebar with actions and server information
 */
function coolify_renderAdminSidebar($params, $serviceInfo, $serverResources)
{
    $output = '';
    
    // Quick Actions Panel
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-bolt"></i> Quick Actions</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    
    $status = isset($serviceInfo['status']) ? $serviceInfo['status'] : 'unknown';
    
    if ($status == 'running') {
        $output .= '<a href="https://' . htmlspecialchars($params['domain']) . '" target="_blank" class="btn btn-success btn-block btn-sm" style="margin-bottom: 8px;">';
        $output .= '<i class="fa fa-external-link"></i> Open Customer Dashboard';
        $output .= '</a>';
    }
    
    $output .= '<button onclick="location.reload();" class="btn btn-info btn-block btn-sm" style="margin-bottom: 8px;">';
    $output .= '<i class="fa fa-refresh"></i> Refresh Status';
    $output .= '</button>';
    
    $output .= '<a href="?action=custom&a=restartService&id=' . $params['serviceid'] . '" class="btn btn-warning btn-block btn-sm" style="margin-bottom: 8px;">';
    $output .= '<i class="fa fa-refresh"></i> Restart Service';
    $output .= '</a>';
    
    $output .= '<a href="?action=custom&a=viewLogs&id=' . $params['serviceid'] . '" class="btn btn-default btn-block btn-sm">';
    $output .= '<i class="fa fa-file-text-o"></i> View Service Logs';
    $output .= '</a>';
    
    $output .= '</div>';
    $output .= '</div>';
    
    // Server Resources Panel
    if ($serverResources) {
        $output .= '<div class="panel panel-default">';
        $output .= '<div class="panel-heading">';
        $output .= '<h4 class="panel-title"><i class="fa fa-server"></i> Server Resources</h4>';
        $output .= '</div>';
        $output .= '<div class="panel-body">';
        
        if (isset($serverResources['cpu_usage'])) {
            $cpuUsage = round($serverResources['cpu_usage'], 1);
            $cpuClass = ($cpuUsage > 80) ? 'danger' : (($cpuUsage > 60) ? 'warning' : 'success');
            $output .= '<p><strong>CPU Usage:</strong> <span class="label label-' . $cpuClass . '">' . $cpuUsage . '%</span></p>';
        }
        
        if (isset($serverResources['memory_usage'])) {
            $memUsage = round($serverResources['memory_usage'], 1);
            $memClass = ($memUsage > 80) ? 'danger' : (($memUsage > 60) ? 'warning' : 'success');
            $output .= '<p><strong>Memory Usage:</strong> <span class="label label-' . $memClass . '">' . $memUsage . '%</span></p>';
        }
        
        if (isset($serverResources['disk_usage'])) {
            $diskUsage = round($serverResources['disk_usage'], 1);
            $diskClass = ($diskUsage > 80) ? 'danger' : (($diskUsage > 60) ? 'warning' : 'success');
            $output .= '<p><strong>Disk Usage:</strong> <span class="label label-' . $diskClass . '">' . $diskUsage . '%</span></p>';
        }
        
        $output .= '</div>';
        $output .= '</div>';
    }
    
    // Service Configuration Panel
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-cog"></i> Configuration</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<p><strong>Service Type:</strong> n8n Automation</p>';
    $output .= '<p><strong>Database:</strong> PostgreSQL 16</p>';
    $output .= '<p><strong>SSL:</strong> Auto (Let\'s Encrypt)</p>';
    $output .= '<p><strong>Backup:</strong> Enabled</p>';
    
    $output .= '<hr style="margin: 10px 0;">';
    $output .= '<a href="?action=custom&a=updateConfig&id=' . $params['serviceid'] . '" class="btn btn-primary btn-block btn-sm">';
    $output .= '<i class="fa fa-edit"></i> Edit Configuration';
    $output .= '</a>';
    $output .= '</div>';
    $output .= '</div>';
    
    return $output;
}

/**
 * Render service logs with enhanced formatting
 */
function coolify_renderEnhancedLogs($logs, $serviceInfo = null)
{
    $output = '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4><i class="fa fa-file-text-o"></i> Service Logs';
    if ($serviceInfo && isset($serviceInfo['name'])) {
        $output .= ' - ' . htmlspecialchars($serviceInfo['name']);
    }
    $output .= ' <small>(Last 200 lines)</small></h4>';
    $output .= '</div>';
    
    $output .= '<div class="panel-body">';
    $output .= '<div class="row">';
    $output .= '<div class="col-md-12">';
    
    // Log Controls
    $output .= '<div class="btn-group" style="margin-bottom: 15px;">';
    $output .= '<button onclick="location.reload();" class="btn btn-default btn-sm">';
    $output .= '<i class="fa fa-refresh"></i> Refresh Logs';
    $output .= '</button>';
    $output .= '<button onclick="copyLogsToClipboard();" class="btn btn-default btn-sm">';
    $output .= '<i class="fa fa-copy"></i> Copy to Clipboard';
    $output .= '</button>';
    $output .= '</div>';
    
    // Log Content
    $output .= '<div id="log-content" style="max-height: 600px; overflow-y: auto; background: #1e1e1e; color: #f8f8f2; padding: 15px; border-radius: 4px; font-family: \'Courier New\', monospace; font-size: 12px; line-height: 1.4;">';
    
    if ($logs && is_array($logs)) {
        foreach ($logs as $logLine) {
            if (is_string($logLine)) {
                $output .= htmlspecialchars($logLine) . "\n";
            } elseif (isset($logLine['message'])) {
                $timestamp = isset($logLine['timestamp']) ? '[' . $logLine['timestamp'] . '] ' : '';
                $message = $timestamp . $logLine['message'];
                
                // Color coding for different log levels
                if (strpos($message, 'ERROR') !== false || strpos($message, 'error') !== false) {
                    $output .= '<span style="color: #ff6b6b;">' . htmlspecialchars($message) . '</span>' . "\n";
                } elseif (strpos($message, 'WARN') !== false || strpos($message, 'warning') !== false) {
                    $output .= '<span style="color: #feca57;">' . htmlspecialchars($message) . '</span>' . "\n";
                } elseif (strpos($message, 'INFO') !== false || strpos($message, 'info') !== false) {
                    $output .= '<span style="color: #48cae4;">' . htmlspecialchars($message) . '</span>' . "\n";
                } else {
                    $output .= htmlspecialchars($message) . "\n";
                }
            }
        }
    } elseif (is_string($logs)) {
        $output .= htmlspecialchars($logs);
    } else {
        $output .= '<span style="color: #6c757d;">No logs available or unable to retrieve logs.</span>';
    }
    
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '<div class="panel-footer">';
    $output .= '<small class="text-muted">';
    $output .= '<i class="fa fa-info-circle"></i> Logs are refreshed each time you view this page. ';
    $output .= 'These show the n8n container output and any errors. ';
    $output .= 'Logs are automatically rotated to prevent excessive disk usage.';
    $output .= '</small>';
    $output .= '</div>';
    $output .= '</div>';
    
    // JavaScript for copy functionality
    $output .= '<script>
    function copyLogsToClipboard() {
        var logContent = document.getElementById("log-content").innerText;
        navigator.clipboard.writeText(logContent).then(function() {
            alert("Logs copied to clipboard!");
        }, function(err) {
            console.error("Could not copy logs: ", err);
        });
    }
    </script>';
    
    return $output;
}
