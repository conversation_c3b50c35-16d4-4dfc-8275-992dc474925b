<?php
/**
 * Welcome Email Template for Coolify n8n Services
 * Professional welcome email sent when n8n service is provisioned
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Generate welcome email content for new n8n service
 */
function coolify_generateWelcomeEmail($params, $serviceInfo = null)
{
    $clientName = $params['clientsdetails']['firstname'] . ' ' . $params['clientsdetails']['lastname'];
    $serviceDomain = $params['domain'];
    $serviceId = $params['serviceid'];
    $productName = $params['productname'];
    
    // Get actual domain from service info if available
    if ($serviceInfo && isset($serviceInfo['fqdn']) && !empty($serviceInfo['fqdn'])) {
        $serviceDomain = $serviceInfo['fqdn'];
    }
    
    $emailContent = [
        'subject' => '🎉 Welcome to n8n! Your Workflow Automation Platform is Ready',
        'html' => coolify_generateWelcomeEmailHTML($clientName, $serviceDomain, $serviceId, $productName, $serviceInfo),
        'text' => coolify_generateWelcomeEmailText($clientName, $serviceDomain, $serviceId, $productName, $serviceInfo)
    ];
    
    return $emailContent;
}

/**
 * Generate HTML version of welcome email
 */
function coolify_generateWelcomeEmailHTML($clientName, $serviceDomain, $serviceId, $productName, $serviceInfo)
{
    $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to n8n - Your Workflow Automation Platform</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; }
        .header p { margin: 10px 0 0 0; font-size: 16px; opacity: 0.9; }
        .content { padding: 30px; }
        .welcome-box { background-color: #f8f9fa; border-left: 4px solid #28a745; padding: 20px; margin: 20px 0; }
        .service-details { background-color: #e3f2fd; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .quick-start { background-color: #fff3cd; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .cta-button { display: inline-block; background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 10px 0; }
        .cta-button:hover { background-color: #218838; }
        .features-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .feature-box { background-color: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .footer { background-color: #343a40; color: white; padding: 20px; text-align: center; font-size: 14px; }
        .footer a { color: #17a2b8; text-decoration: none; }
        @media (max-width: 600px) {
            .features-grid { grid-template-columns: 1fr; }
            .content { padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 Welcome to n8n!</h1>
            <p>Your Workflow Automation Platform is Ready</p>
        </div>
        
        <!-- Main Content -->
        <div class="content">
            <div class="welcome-box">
                <h2>Hello ' . htmlspecialchars($clientName) . '!</h2>
                <p>Congratulations! Your n8n workflow automation platform has been successfully provisioned and is ready to use. You can now start building powerful automations to streamline your business processes.</p>
            </div>
            
            <!-- Service Details -->
            <div class="service-details">
                <h3>📋 Your Service Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Service:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">' . htmlspecialchars($productName) . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Access URL:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><a href="https://' . htmlspecialchars($serviceDomain) . '" style="color: #007bff;">https://' . htmlspecialchars($serviceDomain) . '</a></td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Database:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">PostgreSQL (Included)</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>SSL Certificate:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">Automatic (Let\'s Encrypt)</td>
                    </tr>
                </table>
                
                <div style="text-align: center; margin-top: 20px;">
                    <a href="https://' . htmlspecialchars($serviceDomain) . '" class="cta-button">🚀 Launch n8n Dashboard</a>
                </div>
            </div>
            
            <!-- Quick Start Guide -->
            <div class="quick-start">
                <h3>🏃‍♂️ Quick Start Guide</h3>
                <ol>
                    <li><strong>Access your dashboard:</strong> Click the "Launch n8n Dashboard" button above</li>
                    <li><strong>Create your first workflow:</strong> Click "New Workflow" in the n8n interface</li>
                    <li><strong>Add nodes:</strong> Drag and drop nodes to build your automation</li>
                    <li><strong>Connect services:</strong> Configure each node with your service credentials</li>
                    <li><strong>Test & activate:</strong> Test your workflow and activate it when ready</li>
                </ol>
            </div>
            
            <!-- Features -->
            <h3>✨ What You Can Do with n8n</h3>
            <div class="features-grid">
                <div class="feature-box">
                    <h4>📧 Email Automation</h4>
                    <p>Auto-respond to emails and send notifications</p>
                </div>
                <div class="feature-box">
                    <h4>🔄 Data Synchronization</h4>
                    <p>Keep databases and spreadsheets in sync</p>
                </div>
                <div class="feature-box">
                    <h4>📱 Social Media</h4>
                    <p>Auto-post content across platforms</p>
                </div>
                <div class="feature-box">
                    <h4>🛒 E-commerce</h4>
                    <p>Process orders and update inventory</p>
                </div>
            </div>
            
            <!-- Popular Integrations -->
            <h3>🔌 Popular Integrations Available</h3>
            <p>Connect with over 500 services including:</p>
            <ul style="columns: 2; column-gap: 30px;">
                <li>Google Workspace</li>
                <li>Microsoft 365</li>
                <li>Slack & Discord</li>
                <li>Shopify & WooCommerce</li>
                <li>Airtable & Notion</li>
                <li>GitHub & GitLab</li>
                <li>Stripe & PayPal</li>
                <li>Telegram & WhatsApp</li>
            </ul>
            
            <!-- Resources -->
            <h3>📚 Helpful Resources</h3>
            <ul>
                <li><a href="https://docs.n8n.io/getting-started/" style="color: #007bff;">Official n8n Documentation</a></li>
                <li><a href="https://n8n.io/workflows" style="color: #007bff;">Pre-built Workflow Templates</a></li>
                <li><a href="https://community.n8n.io" style="color: #007bff;">Community Forum</a></li>
                <li><a href="https://www.youtube.com/c/n8n-io" style="color: #007bff;">Video Tutorials</a></li>
            </ul>
            
            <!-- Support -->
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>🆘 Need Help?</h3>
                <p>Our support team is here to help you get the most out of your n8n instance:</p>
                <ul>
                    <li>Check your service status in the WHMCS client area</li>
                    <li>Browse our comprehensive documentation</li>
                    <li>Contact our support team for technical assistance</li>
                </ul>
            </div>
            
            <!-- Next Steps -->
            <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>🎯 Next Steps</h3>
                <ol>
                    <li>Bookmark your n8n dashboard URL</li>
                    <li>Explore the pre-built workflow templates</li>
                    <li>Join the n8n community forum</li>
                    <li>Start with a simple automation (like email notifications)</li>
                    <li>Gradually build more complex workflows</li>
                </ol>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>Thank you for choosing our n8n hosting service!</p>
            <p>If you have any questions, please don\'t hesitate to <a href="#">contact our support team</a>.</p>
            <p style="margin-top: 20px; font-size: 12px; opacity: 0.8;">
                This email was sent regarding your n8n service (ID: ' . htmlspecialchars($serviceId) . ')
            </p>
        </div>
    </div>
</body>
</html>';

    return $html;
}

/**
 * Generate plain text version of welcome email
 */
function coolify_generateWelcomeEmailText($clientName, $serviceDomain, $serviceId, $productName, $serviceInfo)
{
    $text = "🎉 WELCOME TO N8N - YOUR WORKFLOW AUTOMATION PLATFORM IS READY!

Hello " . $clientName . "!

Congratulations! Your n8n workflow automation platform has been successfully
provisioned and is ready to use. You can now start building powerful automations
to streamline your business processes.

📋 YOUR SERVICE DETAILS
========================
Service: " . $productName . "
Access URL: https://" . $serviceDomain . "
Database: PostgreSQL (Included)
SSL Certificate: Automatic (Let's Encrypt)

🚀 ACCESS YOUR DASHBOARD: https://" . $serviceDomain . "

🏃‍♂️ QUICK START GUIDE
===================
1. Access your dashboard: Visit the URL above
2. Create your first workflow: Click \"New Workflow\" in the n8n interface
3. Add nodes: Drag and drop nodes to build your automation
4. Connect services: Configure each node with your service credentials
5. Test & activate: Test your workflow and activate it when ready

✨ WHAT YOU CAN DO WITH N8N
===========================
📧 Email Automation - Auto-respond to emails and send notifications
🔄 Data Synchronization - Keep databases and spreadsheets in sync
📱 Social Media - Auto-post content across platforms
🛒 E-commerce - Process orders and update inventory

🔌 POPULAR INTEGRATIONS AVAILABLE
=================================
Connect with over 500 services including:
• Google Workspace        • Microsoft 365
• Slack & Discord         • Shopify & WooCommerce
• Airtable & Notion       • GitHub & GitLab
• Stripe & PayPal         • Telegram & WhatsApp

📚 HELPFUL RESOURCES
===================
• Official Documentation: https://docs.n8n.io/getting-started/
• Workflow Templates: https://n8n.io/workflows
• Community Forum: https://community.n8n.io
• Video Tutorials: https://www.youtube.com/c/n8n-io

🆘 NEED HELP?
============
Our support team is here to help you get the most out of your n8n instance:
• Check your service status in the WHMCS client area
• Browse our comprehensive documentation
• Contact our support team for technical assistance

🎯 NEXT STEPS
============
1. Bookmark your n8n dashboard URL
2. Explore the pre-built workflow templates
3. Join the n8n community forum
4. Start with a simple automation (like email notifications)
5. Gradually build more complex workflows

Thank you for choosing our n8n hosting service!

If you have any questions, please don't hesitate to contact our support team.

---
This email was sent regarding your n8n service (ID: " . $serviceId . ")";

    return $text;
}

/**
 * Generate a simple welcome email for basic email clients
 */
function coolify_generateSimpleWelcomeEmail($clientName, $serviceDomain, $serviceId, $productName)
{
    $subject = "Welcome to n8n! Your service is ready";

    $message = "Hello " . $clientName . ",

Your n8n workflow automation service has been successfully set up!

Service Details:
- Product: " . $productName . "
- Access URL: https://" . $serviceDomain . "
- Service ID: " . $serviceId . "

To get started:
1. Visit: https://" . $serviceDomain . "
2. Create your first workflow
3. Explore the available integrations

Resources:
- Documentation: https://docs.n8n.io
- Templates: https://n8n.io/workflows
- Community: https://community.n8n.io

Need help? Contact our support team.

Thank you for choosing our service!";

    return [
        'subject' => $subject,
        'message' => $message
    ];
}
