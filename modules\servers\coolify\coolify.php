<?php
/**
 * WHMCS Coolify Server Module
 *
 * This module integrates WHMCS with Coolify to automatically deploy n8n instances
 *
 * <AUTHOR> Name
 * @version 1.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use Illuminate\Database\Capsule\Manager as Capsule;

require_once __DIR__ . '/lib/CoolifyAPI.php';
require_once __DIR__ . '/templates/UserGuideTemplate.php';
require_once __DIR__ . '/templates/WelcomeEmailTemplate.php';

/**
 * Define module related meta data.
 */
function coolify_MetaData()
{
    return array(
        'DisplayName' => 'Coolify n8n',
        'APIVersion' => '1.1',
        'RequiresServer' => true,
        'DefaultNonSSLPort' => '80',
        'DefaultSSLPort' => '443',
        'ServiceSingleSignOnLabel' => 'Open n8n Dashboard',
    );
}

/**
 * Define product configuration options.
 */
function coolify_ConfigOptions()
{
    return array(
        'api_token' => array(
            'FriendlyName' => 'Coolify API Token',
            'Type' => 'password',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your Coolify API Bearer Token',
        ),
        'project_uuid' => array(
            'FriendlyName' => 'Project UUID',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Coolify Project UUID where services will be deployed',
        ),
        'environment_name' => array(
            'FriendlyName' => 'Environment Name',
            'Type' => 'text',
            'Size' => '25',
            'Default' => 'production',
            'Description' => 'Environment name for deployments',
        ),
        'server_uuid' => array(
            'FriendlyName' => 'Server UUID',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Coolify Server UUID where n8n will be deployed',
        ),
        'destination_uuid' => array(
            'FriendlyName' => 'Destination UUID',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Destination UUID for the deployment',
        ),
        'base_domain' => array(
            'FriendlyName' => 'Base Domain',
            'Type' => 'text',
            'Size' => '30',
            'Default' => 'yourdomain.com',
            'Description' => 'Base domain for n8n instances (e.g., customers.yourdomain.com)',
        ),
        'n8n_image' => array(
            'FriendlyName' => 'n8n Docker Image',
            'Type' => 'text',
            'Size' => '30',
            'Default' => 'n8nio/n8n:latest',
            'Description' => 'Docker image to use for n8n deployments',
        ),
        'memory_limit' => array(
            'FriendlyName' => 'Memory Limit (MB)',
            'Type' => 'text',
            'Size' => '10',
            'Default' => '512',
            'Description' => 'Memory limit for n8n container in MB',
        ),
        'coolify_url' => array(
            'FriendlyName' => 'Coolify Instance URL',
            'Type' => 'text',
            'Size' => '50',
            'Default' => 'https://app.coolify.io',
            'Description' => 'Your Coolify instance URL (e.g., https://coolify.yourdomain.com) - without /api/v1',
        ),
        'timezone' => array(
            'FriendlyName' => 'Timezone',
            'Type' => 'dropdown',
            'Options' => 'UTC,Europe/London,Europe/Berlin,Europe/Paris,America/New_York,America/Los_Angeles,Asia/Tokyo,Asia/Shanghai,Australia/Sydney',
            'Default' => 'UTC',
            'Description' => 'Timezone for n8n instance',
        ),
    );
}

/**
 * Provision a new instance of n8n.
 */
function coolify_CreateAccount($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash field.';
        }

        // Validate required configuration options
        $requiredConfigs = [
            'configoption2' => 'Project UUID',
            'configoption3' => 'Environment Name',
            'configoption4' => 'Server UUID',
            'configoption5' => 'Destination UUID',
            'configoption6' => 'Base Domain'
        ];

        foreach ($requiredConfigs as $configKey => $configName) {
            if (empty($params[$configKey])) {
                return "Error: Missing required configuration: $configName. Please configure the server settings.";
            }
        }

        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        // Generate unique service name
        $serviceName = 'n8n-' . strtolower($params['username']) . '-' . time();

        // Get timezone from configuration (default to UTC)
        $timezone = !empty($params['configoption10']) ? $params['configoption10'] : 'UTC';

        // Generate n8n Docker Compose configuration using official template
        $dockerCompose = generateN8nDockerCompose($serviceName, $timezone);
        
        // Prepare service configuration with Docker Compose
        $serviceConfig = array(
            'name' => $serviceName,
            'description' => 'n8n instance for ' . $params['clientsdetails']['firstname'] . ' ' . $params['clientsdetails']['lastname'],
            'project_uuid' => $params['configoption2'],
            'environment_name' => $params['configoption3'],
            'server_uuid' => $params['configoption4'],
            'destination_uuid' => $params['configoption5'],
            'docker_compose_raw' => $dockerCompose,
            'instant_deploy' => true
        );
        
        // Log the service configuration for debugging
        logModuleCall(
            'coolify',
            'CreateAccount_Request',
            $serviceConfig,
            'Sending service creation request',
            '',
            array($apiToken) // Hide API token from logs
        );

        // Create the service
        $result = $api->createService($serviceConfig);

        if ($result && isset($result['uuid'])) {
            // Store service details in WHMCS
            $serviceUuid = $result['uuid'];

            // Get the service domain from Coolify (will be generated automatically)
            $serviceDomain = 'Pending'; // Will be updated once service is deployed
            if (isset($result['fqdn']) && !empty($result['fqdn'])) {
                $serviceDomain = $result['fqdn'];
            } elseif (isset($result['domains']) && !empty($result['domains'])) {
                $serviceDomain = is_array($result['domains']) ? $result['domains'][0] : $result['domains'];
            }

            // Update the service hostname and other details in WHMCS
            $pdo = Capsule::connection()->getPdo();
            $stmt = $pdo->prepare("UPDATE tblhosting SET domain = ?, username = ?, password = ? WHERE id = ?");
            $stmt->execute([$serviceDomain, 'n8n-user', 'Generated by Coolify', $params['serviceid']]);

            // Store additional data in custom fields or notes
            logModuleCall(
                'coolify',
                'CreateAccount_Success',
                $serviceConfig,
                $result,
                '',
                array($apiToken) // Hide API token from logs
            );

            // Store the service UUID for future reference
            // First, ensure the custom field exists
            $fieldId = ensureCustomFieldExists($params['pid'], 'coolify_service_uuid', 'Coolify Service UUID');

            if ($fieldId) {
                // Check if value already exists
                $stmt = $pdo->prepare("SELECT id FROM tblcustomfieldsvalues WHERE fieldid = ? AND relid = ?");
                $stmt->execute([$fieldId, $params['serviceid']]);
                $existing = $stmt->fetch();

                if ($existing) {
                    // Update existing value
                    $stmt = $pdo->prepare("UPDATE tblcustomfieldsvalues SET value = ? WHERE fieldid = ? AND relid = ?");
                    $stmt->execute([$serviceUuid, $fieldId, $params['serviceid']]);
                } else {
                    // Insert new value
                    $stmt = $pdo->prepare("INSERT INTO tblcustomfieldsvalues (fieldid, relid, value) VALUES (?, ?, ?)");
                    $stmt->execute([$fieldId, $params['serviceid'], $serviceUuid]);
                }
            }

            // Send welcome email to customer
            try {
                coolify_sendWelcomeEmail($params, $result);
            } catch (Exception $e) {
                // Log email error but don't fail the provisioning
                logModuleCall('coolify', 'WelcomeEmail_Error', $params, $e->getMessage(), '');
            }

            return 'success';
        } else {
            // Enhanced error reporting
            $errorMessage = 'Failed to create service';
            if (isset($result['message'])) {
                $errorMessage .= ': ' . $result['message'];
            } elseif (isset($result['errors'])) {
                $errorMessage .= ': ' . json_encode($result['errors']);
            } elseif (is_array($result)) {
                $errorMessage .= ': ' . json_encode($result);
            } else {
                $errorMessage .= ': Unknown error';
            }

            // Log the error for debugging
            logModuleCall(
                'coolify',
                'CreateAccount_Error',
                $serviceConfig,
                $result,
                $errorMessage,
                array($apiToken)
            );

            return $errorMessage;
        }
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'CreateAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Suspend an instance.
 */
function coolify_SuspendAccount($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash field.';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->stopService($serviceUuid);
        
        logModuleCall(
            'coolify',
            'SuspendAccount',
            $params,
            $result,
            '',
            array($apiToken)
        );
        
        return 'success';
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'SuspendAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Unsuspend an instance.
 */
function coolify_UnsuspendAccount($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash field.';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->startService($serviceUuid);
        
        logModuleCall(
            'coolify',
            'UnsuspendAccount',
            $params,
            $result,
            '',
            array($apiToken)
        );
        
        return 'success';
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'UnsuspendAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Terminate an instance.
 */
function coolify_TerminateAccount($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash field.';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->deleteService($serviceUuid);
        
        // Clean up custom field data
        $pdo = Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("DELETE FROM tblcustomfieldsvalues WHERE relid = ? AND fieldid = (
            SELECT id FROM tblcustomfields WHERE fieldname = 'coolify_service_uuid' AND type = 'product' AND relid = ? LIMIT 1
        )");
        $stmt->execute([$params['serviceid'], $params['pid']]);
        
        logModuleCall(
            'coolify',
            'TerminateAccount',
            $params,
            $result,
            '',
            array($apiToken)
        );
        
        return 'success';
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'TerminateAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Test connection to Coolify API.
 */
function coolify_TestConnection($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return array(
                'success' => false,
                'error' => 'No API token configured. Please set the Coolify API token in the server Access Hash field.',
            );
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $result = $api->getServers();
        
        if ($result && is_array($result)) {
            $success = true;
            $errorMsg = 'Connection successful. Found ' . count($result) . ' server(s).';
        } else {
            $success = false;
            $errorMsg = 'Failed to connect or retrieve servers.';
        }
        
    } catch (Exception $e) {
        $success = false;
        $errorMsg = 'Connection failed: ' . $e->getMessage();
    }
    
    return array(
        'success' => $success,
        'error' => $errorMsg,
    );
}

/**
 * Admin area output.
 */
function coolify_AdminCustomButtonArray($params)
{
    return array(
        'View n8n Service' => 'viewService',
        'Restart n8n' => 'restartService',
        'View Service Logs' => 'viewLogs',
    );
}

/**
 * Client area output - Enhanced service management dashboard.
 */
function coolify_ClientArea($params)
{
    $serviceUuid = getServiceUuid($params['serviceid']);

    if (!$serviceUuid) {
        return coolify_renderProvisioningTemplate();
    }

    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];

        if (empty($apiToken)) {
            return '<div class="alert alert-danger">API token not configured. Please contact support.</div>';
        }

        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceInfo = $api->getService($serviceUuid);

        if ($serviceInfo) {
            return coolify_renderServiceDashboard($params, $serviceInfo);
        } else {
            return coolify_renderServiceUnavailable($params);
        }

    } catch (Exception $e) {
        return coolify_renderServiceError($params, $e);
    }
}

/**
 * Single Sign-On function.
 */
function coolify_ServiceSingleSignOn($params)
{
    return array(
        'success' => true,
        'redirectTo' => 'https://' . $params['domain'],
    );
}



/**
 * Helper function to get service UUID.
 */
function getServiceUuid($serviceId)
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Get the product ID for this service
        $stmt = $pdo->prepare("SELECT packageid FROM tblhosting WHERE id = ?");
        $stmt->execute([$serviceId]);
        $service = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$service) {
            return false;
        }

        // Get the custom field ID
        $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = ? AND fieldname = 'coolify_service_uuid'");
        $stmt->execute([$service['packageid']]);
        $field = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$field) {
            return false;
        }

        // Get the value
        $stmt = $pdo->prepare("SELECT value FROM tblcustomfieldsvalues WHERE relid = ? AND fieldid = ?");
        $stmt->execute([$serviceId, $field['id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? $result['value'] : false;
    } catch (Exception $e) {
        logModuleCall('coolify', 'getServiceUuid', ['serviceId' => $serviceId], $e->getMessage(), '');
        return false;
    }
}

/**
 * Generate random password.
 */
function generateRandomPassword($length = 16)
{
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $password;
}

/**
 * Generate n8n Docker Compose configuration using official template
 * This uses PostgreSQL database and Coolify's built-in environment variables
 */
function generateN8nDockerCompose($serviceName = 'n8n', $timezone = 'UTC')
{
    // Use official n8n Docker Compose template with PostgreSQL
    $dockerCompose = "services:
  n8n:
    image: docker.n8n.io/n8nio/n8n
    environment:
      - SERVICE_FQDN_N8N_5678
      - 'N8N_EDITOR_BASE_URL=\${SERVICE_FQDN_N8N}'
      - 'WEBHOOK_URL=\${SERVICE_FQDN_N8N}'
      - 'N8N_HOST=\${SERVICE_URL_N8N}'
      - 'GENERIC_TIMEZONE=\${GENERIC_TIMEZONE:-{$timezone}}'
      - 'TZ=\${TZ:-{$timezone}}'
      - DB_TYPE=postgresdb
      - 'DB_POSTGRESDB_DATABASE=\${POSTGRES_DB:-n8n}'
      - DB_POSTGRESDB_HOST=postgresql
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_USER=\$SERVICE_USER_POSTGRES
      - DB_POSTGRESDB_SCHEMA=public
      - DB_POSTGRESDB_PASSWORD=\$SERVICE_PASSWORD_POSTGRES
    volumes:
      - 'n8n-data:/home/<USER>/.n8n'
    depends_on:
      postgresql:
        condition: service_healthy
    healthcheck:
      test:
        - CMD-SHELL
        - 'wget -qO- http://127.0.0.1:5678/'
      interval: 5s
      timeout: 20s
      retries: 10
  postgresql:
    image: 'postgres:16-alpine'
    volumes:
      - 'postgresql-data:/var/lib/postgresql/data'
    environment:
      - POSTGRES_USER=\$SERVICE_USER_POSTGRES
      - POSTGRES_PASSWORD=\$SERVICE_PASSWORD_POSTGRES
      - 'POSTGRES_DB=\${POSTGRES_DB:-n8n}'
    healthcheck:
      test:
        - CMD-SHELL
        - 'pg_isready -U \$\${POSTGRES_USER} -d \$\${POSTGRES_DB}'
      interval: 5s
      timeout: 20s
      retries: 10";

    return $dockerCompose;
}

// ============================================================================
// CLIENT AREA TEMPLATE FUNCTIONS
// ============================================================================

/**
 * Render provisioning template when service is being set up
 */
function coolify_renderProvisioningTemplate()
{
    $output = '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h3 class="panel-title"><i class="fa fa-clock-o"></i> n8n Service Provisioning</h3>';
    $output .= '</div>';
    $output .= '<div class="panel-body text-center">';
    $output .= '<div class="row">';
    $output .= '<div class="col-md-8 col-md-offset-2">';
    $output .= '<i class="fa fa-spinner fa-spin fa-3x text-primary" style="margin-bottom: 20px;"></i>';
    $output .= '<h4>Setting Up Your n8n Instance</h4>';
    $output .= '<p class="text-muted">Your n8n workflow automation service is being provisioned. This typically takes 2-5 minutes.</p>';

    $output .= '<div class="progress" style="margin: 20px 0;">';
    $output .= '<div class="progress-bar progress-bar-striped active" style="width: 60%">';
    $output .= '<span class="sr-only">60% Complete</span>';
    $output .= '</div>';
    $output .= '</div>';

    $output .= '<div class="alert alert-info">';
    $output .= '<strong>What\'s happening:</strong><br>';
    $output .= '• Creating your dedicated n8n container<br>';
    $output .= '• Setting up PostgreSQL database<br>';
    $output .= '• Generating secure access domain<br>';
    $output .= '• Configuring environment variables';
    $output .= '</div>';

    $output .= '<p><small class="text-muted">This page will automatically update once your service is ready. You can also refresh manually.</small></p>';
    $output .= '<button onclick="location.reload();" class="btn btn-default">Refresh Status</button>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';

    return $output;
}

/**
 * Render main service dashboard when service is active
 */
function coolify_renderServiceDashboard($params, $serviceInfo)
{
    $status = isset($serviceInfo['status']) ? $serviceInfo['status'] : 'unknown';
    $statusClass = ($status == 'running') ? 'success' : (($status == 'stopped') ? 'danger' : 'warning');
    $statusText = ($status == 'running') ? 'Online' : (($status == 'stopped') ? 'Offline' : ucfirst($status));

    // Get the actual domain from Coolify service info
    $actualDomain = $params['domain']; // Default to WHMCS domain
    if (isset($serviceInfo['fqdn']) && !empty($serviceInfo['fqdn'])) {
        $actualDomain = $serviceInfo['fqdn'];

        // Update WHMCS domain if it's different
        if ($actualDomain !== $params['domain'] && $actualDomain !== 'Pending') {
            try {
                $pdo = Capsule::connection()->getPdo();
                $stmt = $pdo->prepare("UPDATE tblhosting SET domain = ? WHERE id = ?");
                $stmt->execute([$actualDomain, $params['serviceid']]);
            } catch (Exception $e) {
                // Log but don't fail
                logModuleCall('coolify', 'UpdateDomain', ['domain' => $actualDomain], $e->getMessage(), '');
            }
        }
    } elseif (isset($serviceInfo['domains']) && !empty($serviceInfo['domains'])) {
        $actualDomain = is_array($serviceInfo['domains']) ? $serviceInfo['domains'][0] : $serviceInfo['domains'];
    }

    $output = '<div class="row">';

    // Main Service Panel
    $output .= '<div class="col-md-8">';
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h3 class="panel-title"><i class="fa fa-cogs"></i> Your n8n Workflow Automation</h3>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';

    // Service Status Row
    $output .= '<div class="row">';
    $output .= '<div class="col-md-6">';
    $output .= '<h4><i class="fa fa-info-circle"></i> Service Status</h4>';
    $output .= '<p><strong>Status:</strong> <span class="label label-' . $statusClass . '">' . $statusText . '</span></p>';
    $output .= '<p><strong>Service Name:</strong> ' . htmlspecialchars($serviceInfo['name']) . '</p>';

    if ($actualDomain && $actualDomain !== 'Pending') {
        $output .= '<p><strong>Domain:</strong> <code>' . htmlspecialchars($actualDomain) . '</code></p>';
        $output .= '<p><strong>Access URL:</strong> <a href="https://' . htmlspecialchars($actualDomain) . '" target="_blank" class="btn btn-primary btn-sm"><i class="fa fa-external-link"></i> Open Dashboard</a></p>';
    } else {
        $output .= '<p><strong>Domain:</strong> <span class="text-muted">Being generated by Coolify...</span></p>';
    }
    $output .= '</div>';

    $output .= '<div class="col-md-6">';
    $output .= '<h4><i class="fa fa-calendar"></i> Service Details</h4>';
    $output .= '<p><strong>Created:</strong> ' . (isset($serviceInfo['created_at']) ? date('M j, Y g:i A', strtotime($serviceInfo['created_at'])) : 'N/A') . '</p>';
    $output .= '<p><strong>Last Updated:</strong> ' . (isset($serviceInfo['updated_at']) ? date('M j, Y g:i A', strtotime($serviceInfo['updated_at'])) : 'N/A') . '</p>';
    $output .= '<p><strong>Database:</strong> PostgreSQL 16</p>';
    $output .= '<p><strong>Version:</strong> Latest n8n</p>';
    $output .= '</div>';
    $output .= '</div>';

    // Quick Actions
    if ($status == 'running' && $actualDomain && $actualDomain !== 'Pending') {
        $output .= '<hr>';
        $output .= '<div class="text-center">';
        $output .= '<a href="https://' . htmlspecialchars($actualDomain) . '" target="_blank" class="btn btn-success btn-lg">';
        $output .= '<i class="fa fa-rocket"></i> Launch n8n Workflow Editor';
        $output .= '</a>';
        $output .= '</div>';
    } elseif ($actualDomain === 'Pending' || !$actualDomain) {
        $output .= '<hr>';
        $output .= '<div class="text-center">';
        $output .= '<span class="btn btn-warning btn-lg disabled">';
        $output .= '<i class="fa fa-clock-o"></i> Domain Generating...';
        $output .= '</span>';
        $output .= '</div>';
    }

    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';

    // Sidebar with Quick Actions and Info
    $output .= '<div class="col-md-4">';
    $output .= coolify_renderServiceSidebar($params, $serviceInfo, $actualDomain, $status);
    $output .= '</div>';

    $output .= '</div>';

    // Getting Started Guide
    if ($status == 'running') {
        $output .= coolify_renderGettingStartedGuide($actualDomain);
    }

    // User Guide and Documentation
    $output .= coolify_renderUserGuide($actualDomain);

    return $output;
}

/**
 * Render service sidebar with quick actions and information
 */
function coolify_renderServiceSidebar($params, $serviceInfo, $actualDomain, $status)
{
    $output = '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-bolt"></i> Quick Actions</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';

    if ($status == 'running' && $actualDomain && $actualDomain !== 'Pending') {
        $output .= '<a href="https://' . htmlspecialchars($actualDomain) . '" target="_blank" class="btn btn-success btn-block btn-sm" style="margin-bottom: 10px;">';
        $output .= '<i class="fa fa-external-link"></i> Open n8n Dashboard';
        $output .= '</a>';

        $output .= '<a href="https://' . htmlspecialchars($actualDomain) . '/workflows" target="_blank" class="btn btn-primary btn-block btn-sm" style="margin-bottom: 10px;">';
        $output .= '<i class="fa fa-sitemap"></i> View Workflows';
        $output .= '</a>';

        $output .= '<a href="https://' . htmlspecialchars($actualDomain) . '/executions" target="_blank" class="btn btn-info btn-block btn-sm" style="margin-bottom: 10px;">';
        $output .= '<i class="fa fa-history"></i> Execution History';
        $output .= '</a>';
    } else {
        $output .= '<div class="alert alert-warning">';
        $output .= '<small><i class="fa fa-exclamation-triangle"></i> Service must be running to access dashboard</small>';
        $output .= '</div>';
    }

    $output .= '</div>';
    $output .= '</div>';

    // Service Information Panel
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-info"></i> Service Information</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<p><strong>Service Type:</strong> n8n Automation</p>';
    $output .= '<p><strong>Database:</strong> PostgreSQL</p>';
    $output .= '<p><strong>Storage:</strong> Persistent Volumes</p>';
    $output .= '<p><strong>SSL:</strong> Automatic (Let\'s Encrypt)</p>';
    $output .= '<p><strong>Backups:</strong> Database + Workflows</p>';
    $output .= '</div>';
    $output .= '</div>';

    // Support Panel
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-life-ring"></i> Need Help?</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<p><small>Having issues with your n8n instance?</small></p>';
    $output .= '<a href="#" class="btn btn-default btn-block btn-sm">Contact Support</a>';
    $output .= '<hr style="margin: 10px 0;">';
    $output .= '<p><small><strong>Resources:</strong></p>';
    $output .= '<ul style="font-size: 12px; margin: 0; padding-left: 15px;">';
    $output .= '<li><a href="https://docs.n8n.io" target="_blank">n8n Documentation</a></li>';
    $output .= '<li><a href="https://community.n8n.io" target="_blank">Community Forum</a></li>';
    $output .= '<li><a href="https://n8n.io/workflows" target="_blank">Workflow Templates</a></li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '</div>';

    return $output;
}

/**
 * Render getting started guide for active services
 */
function coolify_renderGettingStartedGuide($actualDomain)
{
    $output = '<div class="panel panel-info">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-graduation-cap"></i> Getting Started with n8n</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';

    $output .= '<div class="row">';
    $output .= '<div class="col-md-4">';
    $output .= '<h5><i class="fa fa-play-circle"></i> Quick Start</h5>';
    $output .= '<ol style="font-size: 13px;">';
    $output .= '<li>Click "Launch n8n" above</li>';
    $output .= '<li>Create your first workflow</li>';
    $output .= '<li>Add nodes to connect services</li>';
    $output .= '<li>Test and activate your workflow</li>';
    $output .= '</ol>';
    $output .= '</div>';

    $output .= '<div class="col-md-4">';
    $output .= '<h5><i class="fa fa-lightbulb-o"></i> Popular Use Cases</h5>';
    $output .= '<ul style="font-size: 13px;">';
    $output .= '<li>Email automation</li>';
    $output .= '<li>Data synchronization</li>';
    $output .= '<li>Social media posting</li>';
    $output .= '<li>File processing</li>';
    $output .= '<li>API integrations</li>';
    $output .= '</ul>';
    $output .= '</div>';

    $output .= '<div class="col-md-4">';
    $output .= '<h5><i class="fa fa-puzzle-piece"></i> Available Integrations</h5>';
    $output .= '<ul style="font-size: 13px;">';
    $output .= '<li>Google Workspace</li>';
    $output .= '<li>Slack & Discord</li>';
    $output .= '<li>Shopify & WooCommerce</li>';
    $output .= '<li>Airtable & Notion</li>';
    $output .= '<li>500+ more services</li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '</div>';

    if ($actualDomain && $actualDomain !== 'Pending') {
        $output .= '<hr>';
        $output .= '<div class="text-center">';
        $output .= '<a href="https://' . htmlspecialchars($actualDomain) . '" target="_blank" class="btn btn-info">';
        $output .= '<i class="fa fa-rocket"></i> Start Building Workflows';
        $output .= '</a>';
        $output .= ' ';
        $output .= '<a href="https://docs.n8n.io/getting-started/" target="_blank" class="btn btn-default">';
        $output .= '<i class="fa fa-book"></i> View Documentation';
        $output .= '</a>';
        $output .= '</div>';
    }

    $output .= '</div>';
    $output .= '</div>';

    return $output;
}

/**
 * Render service unavailable template
 */
function coolify_renderServiceUnavailable($params)
{
    $output = '<div class="panel panel-warning">';
    $output .= '<div class="panel-heading">';
    $output .= '<h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> Service Information Unavailable</h3>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<div class="alert alert-warning">';
    $output .= '<strong>Unable to retrieve service status</strong><br>';
    $output .= 'Your n8n instance may still be starting up or there may be a temporary connectivity issue.';
    $output .= '</div>';

    $output .= '<div class="row">';
    $output .= '<div class="col-md-6">';
    $output .= '<h4>Service Details</h4>';
    $output .= '<p><strong>Domain:</strong> ' . htmlspecialchars($params['domain']) . '</p>';
    $output .= '<p><strong>Service ID:</strong> ' . htmlspecialchars($params['serviceid']) . '</p>';
    $output .= '</div>';
    $output .= '<div class="col-md-6">';
    $output .= '<h4>Try These Steps</h4>';
    $output .= '<ol>';
    $output .= '<li>Wait a few minutes and refresh</li>';
    $output .= '<li>Try accessing your service directly</li>';
    $output .= '<li>Contact support if issues persist</li>';
    $output .= '</ol>';
    $output .= '</div>';
    $output .= '</div>';

    $output .= '<hr>';
    $output .= '<div class="text-center">';
    $output .= '<a href="https://' . htmlspecialchars($params['domain']) . '" target="_blank" class="btn btn-primary">';
    $output .= '<i class="fa fa-external-link"></i> Try Accessing Service';
    $output .= '</a>';
    $output .= ' ';
    $output .= '<button onclick="location.reload();" class="btn btn-default">';
    $output .= '<i class="fa fa-refresh"></i> Refresh Status';
    $output .= '</button>';
    $output .= '</div>';

    $output .= '</div>';
    $output .= '</div>';

    return $output;
}

/**
 * Render service error template
 */
function coolify_renderServiceError($params, $exception)
{
    $output = '<div class="panel panel-warning">';
    $output .= '<div class="panel-heading">';
    $output .= '<h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> Service Status Check Unavailable</h3>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<div class="alert alert-info">';
    $output .= '<strong>Your n8n service is provisioned</strong><br>';
    $output .= 'We cannot check its current status at this time, but you can try accessing it directly.';
    $output .= '</div>';

    $output .= '<div class="row">';
    $output .= '<div class="col-md-6">';
    $output .= '<h4>Access Information</h4>';
    $output .= '<p><strong>Domain:</strong> ' . htmlspecialchars($params['domain']) . '</p>';
    $output .= '<p><strong>Service Type:</strong> n8n Workflow Automation</p>';
    $output .= '</div>';
    $output .= '<div class="col-md-6">';
    $output .= '<h4>What You Can Do</h4>';
    $output .= '<ul>';
    $output .= '<li>Try accessing your service using the link below</li>';
    $output .= '<li>Check back in a few minutes</li>';
    $output .= '<li>Contact support if you need assistance</li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '</div>';

    $output .= '<hr>';
    $output .= '<div class="text-center">';
    $output .= '<a href="https://' . htmlspecialchars($params['domain']) . '" target="_blank" class="btn btn-primary">';
    $output .= '<i class="fa fa-external-link"></i> Access n8n Dashboard';
    $output .= '</a>';
    $output .= '</div>';

    $output .= '</div>';
    $output .= '</div>';

    return $output;
}

/**
 * Send welcome email to customer when n8n service is provisioned
 */
function coolify_sendWelcomeEmail($params, $serviceInfo = null)
{
    try {
        // Generate email content
        $emailData = coolify_generateWelcomeEmail($params, $serviceInfo);

        // Get customer email
        $customerEmail = $params['clientsdetails']['email'];
        $customerName = $params['clientsdetails']['firstname'] . ' ' . $params['clientsdetails']['lastname'];

        // Prepare email parameters for WHMCS
        $emailParams = [
            'messagename' => 'n8n_welcome_email',
            'id' => $params['serviceid'],
            'customtype' => 'general',
            'customsubject' => $emailData['subject'],
            'custommessage' => $emailData['html'],
            'customvars' => [
                'client_name' => $customerName,
                'service_domain' => $params['domain'],
                'service_id' => $params['serviceid'],
                'product_name' => $params['productname']
            ]
        ];

        // Use WHMCS built-in email system
        if (function_exists('sendMessage')) {
            // Try using WHMCS sendMessage function
            $result = sendMessage('General', $params['serviceid'], $emailParams);
        } else {
            // Fallback to direct email sending
            $result = coolify_sendDirectEmail($customerEmail, $emailData['subject'], $emailData['html'], $emailData['text']);
        }

        // Log successful email sending
        logModuleCall('coolify', 'WelcomeEmail_Sent', [
            'customer_email' => $customerEmail,
            'service_id' => $params['serviceid'],
            'subject' => $emailData['subject']
        ], $result, '');

        return $result;

    } catch (Exception $e) {
        logModuleCall('coolify', 'WelcomeEmail_Error', $params, $e->getMessage(), $e->getTraceAsString());
        throw $e;
    }
}

/**
 * Send email directly using PHP mail function as fallback
 */
function coolify_sendDirectEmail($to, $subject, $htmlBody, $textBody)
{
    // Set headers for HTML email
    $headers = "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "From: noreply@" . $_SERVER['HTTP_HOST'] . "\r\n";
    $headers .= "Reply-To: support@" . $_SERVER['HTTP_HOST'] . "\r\n";
    $headers .= "X-Mailer: WHMCS Coolify Module\r\n";

    // Send the email
    $result = mail($to, $subject, $htmlBody, $headers);

    return $result;
}

/**
 * Ensure custom field exists for a product
 */
function ensureCustomFieldExists($productId, $fieldName, $description)
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Check if custom field already exists
        $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = ? AND fieldname = ?");
        $stmt->execute([$productId, $fieldName]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existing) {
            return $existing['id'];
        }

        // Create the custom field
        $stmt = $pdo->prepare("INSERT INTO tblcustomfields (type, relid, fieldname, fieldtype, description, adminonly, required, showorder, showinvoice) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(['product', $productId, $fieldName, 'text', $description, '1', '0', '0', '0']);

        return $pdo->lastInsertId();

    } catch (Exception $e) {
        logModuleCall('coolify', 'ensureCustomFieldExists', compact('productId', 'fieldName', 'description'), $e->getMessage(), '');
        return false;
    }
}

