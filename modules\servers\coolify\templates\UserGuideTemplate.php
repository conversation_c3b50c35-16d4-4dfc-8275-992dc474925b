<?php
/**
 * User Guide Template for Coolify n8n Services
 * Comprehensive documentation and help templates for customers
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Render comprehensive user guide for n8n services
 */
function coolify_renderUserGuide($actualDomain = null)
{
    $output = '<div class="panel panel-info">';
    $output .= '<div class="panel-heading">';
    $output .= '<h3 class="panel-title"><i class="fa fa-book"></i> n8n User Guide & Documentation</h3>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    
    // Quick Start Section
    $output .= '<div class="row">';
    $output .= '<div class="col-md-12">';
    $output .= '<h4><i class="fa fa-rocket"></i> Quick Start Guide</h4>';
    $output .= '<div class="alert alert-success">';
    $output .= '<strong>Welcome to n8n!</strong> Your workflow automation platform is ready to use.';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '<div class="row">';
    
    // Getting Started Column
    $output .= '<div class="col-md-4">';
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h5><i class="fa fa-play"></i> Getting Started</h5>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<ol>';
    $output .= '<li><strong>Access your dashboard</strong><br><small>Click the "Launch n8n" button above</small></li>';
    $output .= '<li><strong>Create your first workflow</strong><br><small>Click "New Workflow" in the dashboard</small></li>';
    $output .= '<li><strong>Add nodes</strong><br><small>Drag and drop nodes to build your automation</small></li>';
    $output .= '<li><strong>Connect services</strong><br><small>Configure each node with your service credentials</small></li>';
    $output .= '<li><strong>Test & activate</strong><br><small>Test your workflow and activate it</small></li>';
    $output .= '</ol>';
    
    if ($actualDomain && $actualDomain !== 'Pending') {
        $output .= '<div class="text-center" style="margin-top: 15px;">';
        $output .= '<a href="https://' . htmlspecialchars($actualDomain) . '" target="_blank" class="btn btn-success btn-sm">';
        $output .= '<i class="fa fa-external-link"></i> Start Building';
        $output .= '</a>';
        $output .= '</div>';
    }
    
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Popular Use Cases Column
    $output .= '<div class="col-md-4">';
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h5><i class="fa fa-lightbulb-o"></i> Popular Use Cases</h5>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<ul>';
    $output .= '<li><strong>Email Automation</strong><br><small>Auto-respond to emails, send notifications</small></li>';
    $output .= '<li><strong>Data Sync</strong><br><small>Keep databases and spreadsheets in sync</small></li>';
    $output .= '<li><strong>Social Media</strong><br><small>Auto-post content across platforms</small></li>';
    $output .= '<li><strong>E-commerce</strong><br><small>Process orders, update inventory</small></li>';
    $output .= '<li><strong>File Processing</strong><br><small>Auto-organize and process files</small></li>';
    $output .= '<li><strong>Notifications</strong><br><small>Send alerts via Slack, Discord, SMS</small></li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Available Integrations Column
    $output .= '<div class="col-md-4">';
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h5><i class="fa fa-puzzle-piece"></i> Available Integrations</h5>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<div class="row">';
    $output .= '<div class="col-xs-6">';
    $output .= '<strong>Productivity:</strong>';
    $output .= '<ul style="font-size: 12px;">';
    $output .= '<li>Google Workspace</li>';
    $output .= '<li>Microsoft 365</li>';
    $output .= '<li>Notion</li>';
    $output .= '<li>Airtable</li>';
    $output .= '<li>Trello</li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '<div class="col-xs-6">';
    $output .= '<strong>Communication:</strong>';
    $output .= '<ul style="font-size: 12px;">';
    $output .= '<li>Slack</li>';
    $output .= '<li>Discord</li>';
    $output .= '<li>Telegram</li>';
    $output .= '<li>WhatsApp</li>';
    $output .= '<li>Email</li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '<div class="row">';
    $output .= '<div class="col-xs-6">';
    $output .= '<strong>E-commerce:</strong>';
    $output .= '<ul style="font-size: 12px;">';
    $output .= '<li>Shopify</li>';
    $output .= '<li>WooCommerce</li>';
    $output .= '<li>Stripe</li>';
    $output .= '<li>PayPal</li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '<div class="col-xs-6">';
    $output .= '<strong>Development:</strong>';
    $output .= '<ul style="font-size: 12px;">';
    $output .= '<li>GitHub</li>';
    $output .= '<li>GitLab</li>';
    $output .= '<li>Jira</li>';
    $output .= '<li>Jenkins</li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '<p style="font-size: 11px; margin-top: 10px;"><strong>+ 500 more integrations available</strong></p>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '</div>';
    
    // Workflow Examples Section
    $output .= '<hr>';
    $output .= '<h4><i class="fa fa-sitemap"></i> Example Workflows</h4>';
    $output .= '<div class="row">';
    
    $output .= '<div class="col-md-6">';
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h6><i class="fa fa-envelope"></i> Email to Slack Notification</h6>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<p><small>Automatically send Slack notifications when important emails arrive.</small></p>';
    $output .= '<ol style="font-size: 12px;">';
    $output .= '<li>Email Trigger → Monitor inbox for specific emails</li>';
    $output .= '<li>Filter → Check if email is from important sender</li>';
    $output .= '<li>Slack → Send notification to team channel</li>';
    $output .= '</ol>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '<div class="col-md-6">';
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-heading">';
    $output .= '<h6><i class="fa fa-shopping-cart"></i> Order Processing Automation</h6>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    $output .= '<p><small>Process new orders and update inventory automatically.</small></p>';
    $output .= '<ol style="font-size: 12px;">';
    $output .= '<li>Shopify Trigger → New order received</li>';
    $output .= '<li>Google Sheets → Update inventory count</li>';
    $output .= '<li>Email → Send confirmation to customer</li>';
    $output .= '</ol>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '</div>';
    
    // Tips and Best Practices
    $output .= '<hr>';
    $output .= '<h4><i class="fa fa-star"></i> Tips & Best Practices</h4>';
    $output .= '<div class="row">';
    $output .= '<div class="col-md-6">';
    $output .= '<div class="alert alert-info">';
    $output .= '<strong><i class="fa fa-lightbulb-o"></i> Pro Tips:</strong>';
    $output .= '<ul style="margin-top: 10px; margin-bottom: 0;">';
    $output .= '<li>Start with simple workflows and gradually add complexity</li>';
    $output .= '<li>Use the "Test Workflow" feature before activating</li>';
    $output .= '<li>Set up error handling for critical workflows</li>';
    $output .= '<li>Use descriptive names for your workflows and nodes</li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '<div class="col-md-6">';
    $output .= '<div class="alert alert-warning">';
    $output .= '<strong><i class="fa fa-exclamation-triangle"></i> Important Notes:</strong>';
    $output .= '<ul style="margin-top: 10px; margin-bottom: 0;">';
    $output .= '<li>Keep your API credentials secure</li>';
    $output .= '<li>Monitor workflow execution logs regularly</li>';
    $output .= '<li>Set appropriate rate limits for API calls</li>';
    $output .= '<li>Backup important workflows regularly</li>';
    $output .= '</ul>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    // Resources Section
    $output .= '<hr>';
    $output .= '<h4><i class="fa fa-external-link"></i> Additional Resources</h4>';
    $output .= '<div class="row">';
    $output .= '<div class="col-md-3">';
    $output .= '<a href="https://docs.n8n.io" target="_blank" class="btn btn-default btn-block">';
    $output .= '<i class="fa fa-book"></i> Official Documentation';
    $output .= '</a>';
    $output .= '</div>';
    $output .= '<div class="col-md-3">';
    $output .= '<a href="https://community.n8n.io" target="_blank" class="btn btn-default btn-block">';
    $output .= '<i class="fa fa-users"></i> Community Forum';
    $output .= '</a>';
    $output .= '</div>';
    $output .= '<div class="col-md-3">';
    $output .= '<a href="https://n8n.io/workflows" target="_blank" class="btn btn-default btn-block">';
    $output .= '<i class="fa fa-download"></i> Workflow Templates';
    $output .= '</a>';
    $output .= '</div>';
    $output .= '<div class="col-md-3">';
    $output .= '<a href="https://www.youtube.com/c/n8n-io" target="_blank" class="btn btn-default btn-block">';
    $output .= '<i class="fa fa-video-camera"></i> Video Tutorials';
    $output .= '</a>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '</div>';
    $output .= '</div>';
    
    return $output;
}

/**
 * Render troubleshooting guide
 */
function coolify_renderTroubleshootingGuide()
{
    $output = '<div class="panel panel-warning">';
    $output .= '<div class="panel-heading">';
    $output .= '<h4 class="panel-title"><i class="fa fa-wrench"></i> Troubleshooting Guide</h4>';
    $output .= '</div>';
    $output .= '<div class="panel-body">';
    
    $output .= '<div class="row">';
    $output .= '<div class="col-md-6">';
    $output .= '<h5>Common Issues</h5>';
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-body">';
    $output .= '<strong>Q: My workflow isn\'t triggering</strong><br>';
    $output .= '<small>A: Check if the workflow is activated and the trigger is properly configured.</small><br><br>';
    $output .= '<strong>Q: API connection failed</strong><br>';
    $output .= '<small>A: Verify your API credentials and check rate limits.</small><br><br>';
    $output .= '<strong>Q: Workflow execution failed</strong><br>';
    $output .= '<small>A: Check the execution logs for error details.</small>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '<div class="col-md-6">';
    $output .= '<h5>Getting Help</h5>';
    $output .= '<div class="panel panel-default">';
    $output .= '<div class="panel-body">';
    $output .= '<p><strong>Need assistance?</strong></p>';
    $output .= '<ul>';
    $output .= '<li>Check the execution logs in your n8n dashboard</li>';
    $output .= '<li>Search the community forum for similar issues</li>';
    $output .= '<li>Contact our support team with specific error messages</li>';
    $output .= '</ul>';
    $output .= '<a href="#" class="btn btn-primary btn-sm">Contact Support</a>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '</div>';
    $output .= '</div>';
    
    return $output;
}
